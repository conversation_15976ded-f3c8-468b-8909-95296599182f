

<template>
  <div>
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">

      <cascading-category
        class="searchValue"
        style="flex: 0 1 40%"
        :show-all-levels="false"
        :original-value.sync="queryParams.categoryIds"></cascading-category>
<!---->

      <el-select
        style="width: 80px"
        v-model="queryParams.year"
        :placeholder="$t('年份')"
      >
        <el-option label="2025" value="2025" />
        <el-option label="2024" value="2024" />
        <el-option label="2023" value="2023" />
      </el-select>
      <el-select
        style="width: 80px"
        v-model="queryParams.month"
        :placeholder="$t('月份')"
      >
        <el-option v-for="item in 12"
                   :key="item"
                   :label="item + '月'"
                   :value="item"
        />

      </el-select>
      <el-button plain type="primary" @click="queryParams.pageNo = 1;doSearch();">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
    </div>
    <div style="margin: 5px 0">
      <el-button type="primary" icon="el-icon-download" @click="downloadReport">下载报告</el-button>
      <el-button v-if="showMode === 'list'" plain @click="showMode = 'chart';getChartData()">查看图表</el-button>
      <el-button v-if="showMode === 'chart'" type="primary"  @click="showMode = 'list'">查看表格</el-button>

    </div>
    <div v-if="showMode === 'list'">
      <!-- 汇总报告表格 -->
      <el-table
        :data="list"
        border
        stripe
        v-loading="loading"
      >
        <el-table-column
          prop="category"
          label="物料类别"
          align="center"
          min-width="120"
        >
        </el-table-column>
        <el-table-column label="年累计平均" align="center">
          <el-table-column
            v-for="item in badTypeList"
            prop="item"
            :label="getDictDataLabel(DICT_TYPE.SCAR_DEFECT_TYPE, item.replace('pi_', ''))"
            align="center"
            min-width="120"
          />

        </el-table-column>
        <el-table-column
          prop="defectTypeTotal"
          label="总数"
          align="center"
          min-width="120"
        />
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="doSearch"
      />
    </div>
    <div v-if="showMode === 'chart'">
      <!-- 饼图展示 -->
      <div class="pie-charts-container">
        <!-- 所有供应商汇总饼图 -->
        <div class="pie-chart-item" v-if="chartData.allSupplierDefectTypeData">
          <h3 class="chart-title">{{ chartData.allSupplierDefectTypeData.nameShort }}</h3>
          <div ref="chartAll" class="pie-chart"></div>
        </div>

        <!-- 各个供应商饼图 -->
        <div
          class="pie-chart-item"
          v-for="(supplier, index) in chartData.supplierDefectTypeDataList"
          :key="supplier.nameShort"
        >
          <h3 class="chart-title">{{ supplier.nameShort }}</h3>
          <div :ref="`chart${index}`" class="pie-chart"></div>
        </div>
      </div>
    </div>


  </div>
</template>
<script >
import {
  exportDefectTypeStatisticsCategoryExcel,
  getDefectTypeStatisticsCategory, getDefectTypeStatisticsCategoryChart

} from '@/api/scar/report'
import * as echarts from 'echarts'
import { getSupplierDetail } from '@/api/scar'
import { getDictDataLabel } from '@/utils/dict'
import CascadingCategory from '@/components/CascadingCategory/index.vue'

export default {
  name: 'materialReport',
  components: { CascadingCategory },
  data() {
    return {
      loading: false,
      list: [],
      total: 0,
      queryParams: {
        categoryIds: [],
        year: '2025',
        month: 1,
        pageNo: 1,
        pageSize: 10
      },
      showMode: 'list',
      chartInstances: [], // 改为数组存储多个图表实例
      chartData: {
        supplierDefectTypeDataList: [],
        allSupplierDefectTypeData: null
      },
      supplierList: [],
      badTypeList: []
    }
  },
  mounted() {
    this.initData()
  },
  beforeDestroy() {
    // 销毁所有图表实例
    this.chartInstances.forEach(instance => {
      if (instance) {
        instance.dispose()
      }
    })
    this.chartInstances = []
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    getDictDataLabel,

    initData() {
      this.doSearch()
    },
    doSearch() {
      this.loading = true
      getDefectTypeStatisticsCategory(this.queryParams).then(res => {
        if (res.code === 0) {
          if (res.data.list.length){
            // 记录不良类型
            this.badTypeList = Object.keys(res.data.list[0]).filter(a => a.indexOf('pi_') > -1)
          }
          this.list = res.data.list || []
          this.total = res.data.total || 0
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(error => {
        console.error('查询失败:', error)
        this.$message.error('查询失败，请稍后重试')
      }).finally(() => {
        this.loading = false
      })
    },
    resetQuery() {
      this.queryParams = {
        supplierIds: [],
        year: '2025',
        month: 1,
        pageNo: 1,
        pageSize: 10
      }
      this.doSearch()
    },
    getChartData(){
      getDefectTypeStatisticsCategoryChart(this.queryParams).then(res => {
        if (res.code === 0) {
          this.chartData = res.data
          this.$nextTick(() => {
            this.initChart()
          })
        } else {
          this.$message.error(res.msg || '获取图表数据失败')
        }
      }).catch(error => {
        console.error('获取图表数据失败:', error)
        this.$message.error('获取图表数据失败，请稍后重试')
      })
    },
    initChart() {
      // 销毁已存在的图表实例
      this.chartInstances.forEach(instance => {
        if (instance) {
          instance.dispose()
        }
      })
      this.chartInstances = []

      // 使用setTimeout确保DOM完全渲染
      setTimeout(() => {
        try {
          // 创建所有供应商汇总饼图
          if (this.chartData.allSupplierDefectTypeData && this.$refs.chartAll) {
            const allChart = echarts.init(this.$refs.chartAll)
            const allOption = this.createPieOption(this.chartData.allSupplierDefectTypeData)
            allChart.setOption(allOption)
            this.chartInstances.push(allChart)
          }

          // 创建各个供应商饼图
          if (this.chartData.supplierDefectTypeDataList && this.chartData.supplierDefectTypeDataList.length > 0) {
            this.chartData.supplierDefectTypeDataList.forEach((supplier, index) => {
              const chartRef = this.$refs[`chart${index}`]
              if (chartRef) {
                // 处理动态ref可能返回数组的情况
                const chartElement = Array.isArray(chartRef) ? chartRef[0] : chartRef
                if (chartElement && chartElement.offsetWidth > 0) {
                  const chart = echarts.init(chartElement)
                  const option = this.createPieOption(supplier)
                  chart.setOption(option)
                  this.chartInstances.push(chart)
                }
              }
            })
          }

          // 监听窗口大小变化
          window.addEventListener('resize', this.handleResize)
        } catch (error) {
          console.error('初始化图表失败:', error)
          this.$message.error('图表初始化失败，请刷新页面重试')
        }
      }, 100)
    },
    createPieOption(supplierData) {
      // 过滤掉值为0的数据
      const validData = supplierData.pieSeriesDatas.filter(item => item.value > 0)

      return {
        // title: {
        //   text: supplierData.nameShort,
        //   left: 'center',
        //   top: 20,
        //   textStyle: {
        //     fontSize: 16,
        //     fontWeight: 'bold'
        //   }
        // },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          data: validData.map(item => item.name)
        },
        series: [
          {
            name: '缺陷类型',
            type: 'pie',
            radius: '60%',
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: validData.length > 0 ? validData : [
              { name: '暂无数据', value: 1, itemStyle: { color: '#e0e0e0' } }
            ]
          }
        ]
      }
    },
    handleResize() {
      this.chartInstances.forEach(instance => {
        if (instance) {
          instance.resize()
        }
      })
    },
    downloadReport() {
      exportDefectTypeStatisticsCategoryExcel(this.queryParams).then(res => {
        this.$download.excel(res, '汇总报告.xlsx')
      })
    },
    doGetSupplierList(query) {
      if (query) {
        getSupplierDetail({
          fuzzySupplierName: query
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
  }
}
</script>

<style scoped lang="scss">
.pie-charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  padding: 20px;
}

.pie-chart-item {
  flex: 0 0 calc(50% - 10px);
  min-width: 400px;
  max-width: 500px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-title {
  text-align: center;
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.pie-chart {
  width: 100%;
  height: 400px;
}

@media (max-width: 1200px) {
  .pie-chart-item {
    flex: 0 0 100%;
    min-width: 300px;
  }
}
</style>

