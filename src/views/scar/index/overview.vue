<template>
  <div class="overview-container">
    <!-- 顶部Tab导航 -->
    <div class="tab-navigation">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="overview-tabs">
        <el-tab-pane label="SCAR 报告汇总" name="summary"></el-tab-pane>
        <el-tab-pane label="供应商 SCAR回复周期" name="response-cycle"></el-tab-pane>
        <el-tab-pane label="不良类型分析" name="defect-analysis"></el-tab-pane>
        <el-tab-pane label="物料类别报告" name="material-report"></el-tab-pane>
        <el-tab-pane label="供应商单个SCAR报告" name="supplier-report"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content">
      <!-- SCAR 报告汇总 -->
      <div v-if="activeTab === 'summary'" class="tab-panel">
        <report-oview />

      </div>

      <!-- 供应商 SCAR回复周期 -->
      <div v-if="activeTab === 'response-cycle'" class="tab-panel">
        <response-cycle />

      </div>

      <!-- 不良类型分析 -->
      <div v-if="activeTab === 'defect-analysis'" class="tab-panel">
        <defect-analysis />
      </div>

      <!-- 物料类别报告 -->
      <div v-if="activeTab === 'material-report'" class="tab-panel">
        <material-report />
      </div>

      <!-- 供应商单个SCAR报告 -->
      <div v-if="activeTab === 'supplier-report'" class="tab-panel">
        <supplier-report></supplier-report>
        <!-- 这里放置供应商单个SCAR报告的内容 -->
      </div>
    </div>
  </div>
</template>

<script>
import { DICT_TYPE } from '@/utils/dict'
import ReportOview from '@/views/scar/index/overview/reportOview.vue'
import ResponseCycle from '@/views/scar/index/overview/responseCycle.vue'
import DefectAnalysis from '@/views/scar/index/overview/defectAnalysis.vue'
import MaterialReport from '@/views/scar/index/overview/materialReport.vue'
import SupplierReport from '@/views/scar/index/overview/supplierReport.vue'
import { getScarReportYear } from '@/api/scar/report'

export default {
  name: 'ScarOverview',
  components: { SupplierReport, MaterialReport, DefectAnalysis, ResponseCycle,  ReportOview },
  data() {
    return {
      DICT_TYPE,
      activeTab: 'summary', // 默认激活第一个tab
      itemArr: [],
      recentRecords: [],
      queryParams: {
        searchText: ''
      }
    }
  },
  mounted() {
    this.init()

  },
  methods: {
    init(){
      getScarReportYear().then(res=>{
        this.yearList = res.data
        console.log(res)
      })
    },
    // Tab切换处理
    handleTabClick(tab) {

    }
  }
}
</script>

<style lang="scss" scoped>
.overview-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Tab导航样式
.tab-navigation {
  margin-bottom: 20px;

  .overview-tabs {
    width: 100%;

    // 自定义tab样式
    :deep(.el-tabs__header) {
      margin: 0;
      border-bottom: 2px solid #e4e7ed;
    }

    :deep(.el-tabs__nav-wrap) {
      padding: 0;
    }

    :deep(.el-tabs__nav) {
      width: 100%;
      display: flex;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 10px;
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;

      &:hover {
        color: #4996b8;
        background-color: #f5f7fa;
      }

      &.is-active {
        color: #4996b8;
        border-bottom-color: #4996b8;
        background-color: rgba(73, 150, 184, 0.07);
      }
    }

    :deep(.el-tabs__active-bar) {
      display: none; // 隐藏默认的激活条，使用自定义样式
    }
  }
}

// Tab内容区域
.tab-content {
  flex: 1;
  overflow: auto;

  .tab-panel {
    min-height: 500px;

    .panel-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}

// 原有样式保持
.charts-section {
  margin: 20px 0;
}

.chart-card {
  height: 400px;

  .chart-container {
    height: 320px;
  }
}

.table-section {
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .tab-navigation {
    .overview-tabs {
      :deep(.el-tabs__item) {
        font-size: 12px;
        padding: 0 5px;
        height: 45px;
        line-height: 45px;
      }
    }
  }
}
</style>
