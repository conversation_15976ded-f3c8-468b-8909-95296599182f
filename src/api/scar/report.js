import request from '@/utils/request'


// SCAR报告汇总
export function getScarReportYear(data) {
  return request({
    url: '/scar/report/get-scar-report-year',
    method: 'get',
    data
  })
}


// SCAR报告汇总
export function getSummaryReportPage(data) {
  return request({
    url: '/scar/report/get-summary-report',
    method: 'post',
    data
  })
}

// SCAR报告汇总图表
export function getSummaryReportChart(data) {
  return request({
    url: '/scar/report/get-summary-report-chart',
    method: 'post',
    data
  })
}

// SCAR报告汇总下载
export function exportSummaryReportExcel(data) {
  return request({
    url: '/scar/report/export-summary-report-execl',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// SCAR回复周期
export function getReplyCyclePage(data) {
  return request({
    url: '/scar/report/get-reply-cycle',
    method: 'post',
    data
  })
}

// SCAR回复周期图表
export function getReplyCycle<PERSON>hart(data) {
  return request({
    url: '/scar/report/get-reply-cycle-chart',
    method: 'post',
    data
  })
}

// SCAR回复周期下载
export function exportReplyCycleExcel(data) {
  return request({
    url: '/scar/report/export-reply-cycle-execl',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 不良类型分析
export function getDefectTypeStatistics(data) {
  return request({
    url: '/scar/report/get-defect-type-statistics',
    method: 'post',
    data
  })
}

// 不良类型分析图表
export function getDefectTypeStatisticsChart(data) {
  return request({
    url: '/scar/report/get-defect-type-statistics-chart',
    method: 'post',
    data
  })
}

// 不良类型分析下载
export function exportDefectTypeStatisticsExcel(data) {
  return request({
    url: '/scar/report/export-defect-type-statistics-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 物料类别报告
export function getDefectTypeStatisticsCategory(data) {
  return request({
    url: '/scar/report/get-defect-type-statistics-category',
    method: 'post',
    data
  })
}

// 物料类别报告图表
export function getDefectTypeStatisticsCategoryChart(data) {
  return request({
    url: '/scar/report/get-defect-type-statistics-category-chart',
    method: 'post',
    data
  })
}

// 物料类别报告下载
export function exportDefectTypeStatisticsCategoryExcel(data) {
  return request({
    url: '/scar/report/export-defect-type-statistics-category-excel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 供应商单个SCAR报告
export function getSingleReportPage(data) {
  return request({
    url: '/scar/report/get-single-report',
    method: 'post',
    data
  })
}

// 供应商单个SCAR报告图表
export function getSingleReportChart(data) {
  return request({
    url: '/scar/report/get-single-report-chart',
    method: 'post',
    data
  })
}

// 供应商单个SCAR报告下载
export function exportSingleReportExcel(data) {
  return request({
    url: '/scar/report/export-single-report-execl',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
